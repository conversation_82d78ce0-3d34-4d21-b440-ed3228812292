﻿using App.ECommerce.Helpers.Implement;
using App.ECommerce.Helpers.Interface;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Dtos.ResultDtos;
using App.ECommerce.Resource.Model;

using AutoMapper;

using MongoDB.Driver;

using Telegram.Bot.Types;

using static App.ECommerce.Resource.Enums.AffiliationEnum;

namespace App.ECommerce.ProcessFlow
{
    public class CommissionsReportFlow : ICommissionsReportFlow
    {
        private readonly IUserRepository _userRepository;
        private readonly IOrderRepository _orderRepository;
        private readonly ICommissionsConfigRepository _commissionsRepository;
        private readonly ICommissionOrderFlow _commissionOrderFlow;
        private readonly ICommissionsReportRepository _commissionsReportRepository;
        private readonly ICommissionsConfigHelpers _commissionsConfigHelpers;
        private readonly ICommissionsCalculatorHeplers _commissionsCalculatorHeplers;
        private readonly IMapper _mapper;

        public CommissionsReportFlow(IUserRepository userRepository,
                                    IOrderRepository orderRepository,
                                    ICommissionsConfigRepository commissionsRepository,
                                    ICommissionOrderFlow commissionOrderFlow,
                                    ICommissionsReportRepository commissionsReportRepository,
                                    ICommissionsConfigHelpers commissionsConfigHelpers,
                                    ICommissionsCalculatorHeplers commissionsCalculatorHeplers,
                                    IMapper mapper)
        {
            _userRepository = userRepository;
            _orderRepository = orderRepository;
            _commissionsRepository = commissionsRepository;
            _commissionOrderFlow = commissionOrderFlow;
            _commissionsReportRepository = commissionsReportRepository;
            _commissionsConfigHelpers = commissionsConfigHelpers;
            _commissionsCalculatorHeplers = commissionsCalculatorHeplers;
            _mapper = mapper;
        }

        #region GetCommissionReport
        private MonthComparison CompareMonthYearToCurrent(int inputMonth, int inputYear)
        {
            var inputTotal = inputYear * 12 + inputMonth;
            var current = DateTime.Now;
            var currentTotal = current.Year * 12 + current.Month;
            var previousTotal = currentTotal - 1;

            return inputTotal switch
            {
                var x when x > currentTotal => MonthComparison.Future,
                var x when x == currentTotal => MonthComparison.Current,
                var x when x == previousTotal => MonthComparison.Previous,
                _ => MonthComparison.Past
            };
        }

        public async Task<PagingResult<CommissionReportResultDto>> GetCommissionReport(GetPartnerCommissionsByMonthYearInputDto inputDto)
        {
            var config = await _commissionsRepository.FindCommissionsConfig(inputDto.ShopId);
            if (config == null) return null;

            var compareResult = CompareMonthYearToCurrent(inputDto.Month, inputDto.Year);
            var paymentDueDay = config.AdvancedCommissionsConfig.PaymentDue;
            var currentDay = DateTime.Now.Day;

            var result = new PagingResult<CommissionReportResultDto>
            {
                Result = new List<CommissionReportResultDto>()
            };

            //Kiểm tra tháng liền trước đã thanh toán chưa (Case:đã thanh toán xong người dùng thay đổi ngày thanh toán)
            if (compareResult == MonthComparison.Previous)
            {
                result = await GetPaidCommissionReport(inputDto);
                if (result.Total > 0)
                {
                    return result;
                }
            }

            return compareResult switch
            {
                MonthComparison.Future => result,
                MonthComparison.Current => await GetUnpaidCommissionReport(inputDto),
                MonthComparison.Previous => currentDay < paymentDueDay
                    ? await GetUnpaidCommissionReport(inputDto) // Tháng trước chưa thanh toán
                    : await GetPaidCommissionReport(inputDto), // Tháng trước đã thanh toán
                MonthComparison.Past => await GetPaidCommissionReport(inputDto),
                _ => result
            };
        }

        private async Task<PagingResult<CommissionReportResultDto>> GetUnpaidCommissionReport(GetPartnerCommissionsByMonthYearInputDto inputDto)
        {
            var partnerCommissions = await GetPartnerCommissionsByMonthAndYear(inputDto);
            return new PagingResult<CommissionReportResultDto>
            {
                Total = partnerCommissions.Total,
                Result = _mapper.Map<List<CommissionReportResultDto>>(partnerCommissions.Result)
                    .Select(commissionReport =>
                    {
                        commissionReport.Month = inputDto.Month;
                        commissionReport.Year = inputDto.Year;
                        commissionReport.PaymentStatus = PaymentStatus.Unpaid;
                        commissionReport.Created = null;
                        return commissionReport;
                    })
                .ToList()
            };
        }

        public async Task<PagingResult<CommissionReportResultDto>> GetPaidCommissionReport(GetPartnerCommissionsByMonthYearInputDto inputDto)
        {
            var partnerCommissions = await _commissionsReportRepository.GetCommissionsReport(inputDto);
            return new PagingResult<CommissionReportResultDto>
            {
                Total = partnerCommissions.Total,
                Result = _mapper.Map<List<CommissionReportResultDto>>(partnerCommissions.Result)
                    .Select(commissionReport =>
                    {
                        commissionReport.NetAmount = commissionReport.CommissionValue - commissionReport.PersonalIncomeTax;
                        return commissionReport;
                    })
                    .ToList()
            };
        }
        #endregion

        #region ApprovePaymentForCommission

        #endregion

        #region GetPartnerCommissionsByMonthAndYear
        public async Task<PagingResult<CommissionByMonthAndYearResultDto>> GetPartnerCommissionsByMonthAndYear(GetPartnerCommissionsByMonthYearInputDto getPartnerCommissionsByMonthYearInputDto)
        {
            // Bước 1: lấy config hoa hồng của shop từ CommissionsConfigRepository
            var commissionConfig = await _commissionsRepository.FindCommissionsConfig(getPartnerCommissionsByMonthYearInputDto.ShopId);
            if (commissionConfig == null)
            {
                return null;
            }

            // Bước 2: Lấy thông tin F0, F1, F2 từ UserRepository
            F0UsersWithF1F2CountsInputDto inputDto = new F0UsersWithF1F2CountsInputDto
            {
                Paging = getPartnerCommissionsByMonthYearInputDto.Paging,
                ShopId = commissionConfig.ShopId,
            };
            var intermediateDtos = await GetF0UsersWithF1F2Counts(inputDto);

            // Bước 3: Chuẩn bị dữ liệu và tính doanh thu, hoa hồng cho tháng được chọn
            var dateInMonth = GetMonthDateRange(getPartnerCommissionsByMonthYearInputDto.Month, getPartnerCommissionsByMonthYearInputDto.Year);
            CalculateRevenueAndCommissionInputDto calucalteDto = new CalculateRevenueAndCommissionInputDto()
            {
                IntermediateUnpaidCommissionResultDtos = intermediateDtos,
                DateFrom = dateInMonth.startDate,
                DateTo = dateInMonth.endDate
            };
            // Bước 4: Tính tổng Price từ OrderRepository và ánh xạ sang DTO cuối cùng
            var unpaidCommissionResults = await CalculateRevenueAndCommission(calucalteDto);

            return unpaidCommissionResults;
        }

        private async Task<PagingResult<IntermediateUnpaidCommissionResultDto>> GetF0UsersWithF1F2Counts(F0UsersWithF1F2CountsInputDto inputDto)
        {
            // Bước 1: Lấy danh sách F0 users với phân trang
            var f0UsersPaging = _userRepository.GetF0Users(inputDto.Paging, inputDto.ShopId);

            // Bước 2: Lấy số lượng và danh sách userId của F1 và F2 cho từng F0
            var result = new PagingResult<IntermediateUnpaidCommissionResultDto>
            {
                Result = new List<IntermediateUnpaidCommissionResultDto>(),
                Total = f0UsersPaging.Total
            };

            foreach (var f0 in f0UsersPaging.Result)
            {
                // Đếm F1 và lấy danh sách F1
                var (f1Count, f1UserIds) = await _userRepository.GetF1Users(f0.UserId);

                // Đếm F2 và lấy danh sách F2
                var (f2Count, f2UserIds) = await _userRepository.GetF2Users(f1UserIds);

                // Tạo DTO trung gian
                result.Result.Add(new IntermediateUnpaidCommissionResultDto
                {
                    UserId = f0.UserId,
                    FullName = f0.Fullname,
                    ReferralCode = f0.ReferralCode,
                    F1Quantity = f1Count,
                    F2Quantity = f2Count,
                    BankAccountName = f0.BankAccountName,
                    BankName = f0.BankName,
                    PaymentType = f0.PaymentType,
                    BankAccountNumber = f0.BankAccountNumber,
                });
            }
            return result;
        }

        private async Task<PagingResult<CommissionByMonthAndYearResultDto>> CalculateRevenueAndCommission(CalculateRevenueAndCommissionInputDto input)
        {
            var result = new PagingResult<CommissionByMonthAndYearResultDto>
            {
                Result = new List<CommissionByMonthAndYearResultDto>(),
                Total = input.IntermediateUnpaidCommissionResultDtos.Total,
            };

            var orderFilterByDateTime = CreateOrderFilter(input.DateFrom, input.DateTo);

            foreach (var dto in input.IntermediateUnpaidCommissionResultDtos.Result)
            {
                var commissionResult = await CalculateCommissionForUser(dto, orderFilterByDateTime);

                // Chỉ thêm vào kết quả nếu CommissionValue > 0
                if (commissionResult.CommissionValue > 0)
                {
                    result.Result.Add(commissionResult);
                }
            }

            // Cập nhật lại Total để phản ánh số lượng thực tế sau khi filter
            result.Total = result.Result.Count;

            return result;
        }
        // Tạo bộ lọc đơn hàng theo thời gian và trạng thái
        private FilterDefinition<Order> CreateOrderFilter(DateTime dateFrom, DateTime dateTo)
        {
            return Builders<Order>.Filter.Gte(o => o.CompletedAt, dateFrom) &
                Builders<Order>.Filter.Lte(o => o.CompletedAt, dateTo) &
                Builders<Order>.Filter.Eq(o => o.StatusOrder, TypeOrderStatus.Success);
        }

        // Tính toán hoa hồng cho một người dùng
        private async Task<CommissionByMonthAndYearResultDto> CalculateCommissionForUser(
            IntermediateUnpaidCommissionResultDto dto,
            FilterDefinition<Order> orderFilter)
        {

            var orders = await _orderRepository.GetOrdersByFilter(orderFilter);
            var (totalRevenue, totalCommission) = _commissionsCalculatorHeplers.CalculateCommissionAndRevenue(orders, dto.UserId);
            var personalIncomeTax = CalculatePersonalIncomeTax(totalCommission);
            var netAmount = totalCommission - personalIncomeTax;

            return new CommissionByMonthAndYearResultDto
            {
                UserId = dto.UserId,
                ReferralCode = dto.ReferralCode,
                FullName = dto.FullName,
                F1Quantity = dto.F1Quantity,
                F2Quantity = dto.F2Quantity,
                Revenue = totalRevenue,
                CommissionValue = totalCommission,
                PersonalIncomeTax = personalIncomeTax,
                NetAmount = netAmount,
                BankAccountName = dto.BankAccountName,
                PaymentType = dto.PaymentType,
                BankName = dto.BankName,
                BankAccountNumber = dto.BankAccountNumber
            };
        }

        // Tính thuế thu nhập cá nhân
        private decimal CalculatePersonalIncomeTax(decimal commissionValue, decimal taxThreshold = 2000000m, decimal taxRate = 0.1m)
        {
            return commissionValue > taxThreshold ? commissionValue * taxRate : 0m;
        }

        // Month Year -> startDate, endDate in this month
        private (DateTime startDate, DateTime endDate) GetMonthDateRange(int month, int year)
        {
            // Ngày đầu tiên của tháng
            var startDate = new DateTime(year, month, 1);

            // Ngày cuối cùng của tháng: lấy ngày đầu tiên của tháng sau và trừ đi 1 ngày
            var endDate = startDate.AddMonths(1).AddDays(-1);

            // Đảm bảo thời gian là 00:00:00 cho startDate và 23:59:59 cho endDate
            startDate = startDate.Date; // 00:00:00
            endDate = endDate.Date.AddHours(23).AddMinutes(59).AddSeconds(59); // 23:59:59

            return (startDate, endDate);
        }
        #endregion
    }
}