using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Model;

using AutoMapper;

using log4net;

using MongoDB.Bson;

using static App.ECommerce.Resource.Enums.AffiliationEnum;

namespace App.ECommerce.ProcessFlow.Implement;

public class AffiliateFlow : IAffiliateFlow
{
    private readonly ILog _log = log4net.LogManager.GetLogger(typeof(AffiliateFlow));
    private readonly ICommissionsReportRepository _commissionsReportRepository;
    private readonly ICommissionsConfigRepository _commissionsConfigRepository;
    private readonly ICommissionsReportFlow _commissionReportFlow;
    private readonly IShopRepository _shopRepository;
    private readonly IUserRepository _userRepository;
    private readonly IMapper _mapper;

    public AffiliateFlow(
        ICommissionsReportFlow commissionReportFlow,
        ICommissionsReportRepository commissionsReportRepository,
        ICommissionsConfigRepository commissionsConfigRepository,
        IShopRepository shopRepository,
        IUserRepository userRepository,
        IMapper mapper)
    {
        _commissionsReportRepository = commissionsReportRepository;
        _commissionsConfigRepository = commissionsConfigRepository;
        _commissionReportFlow = commissionReportFlow;
        _shopRepository = shopRepository;
        _userRepository = userRepository;
        _mapper = mapper;
    }

    /// <summary>
    /// Xử lý thanh toán hoa hồng cho tất cả shop đang hoạt động.
    /// Nếu shop có PaymentDue là ngày hôm nay thì thực hiện thanh toán hoa hồng (Paid),
    /// ngược lại chỉ tính toán và lưu trạng thái Unpaid. Tránh tạo bản ghi trùng lặp bằng cách kiểm tra trạng thái hiện tại.
    /// </summary>
    /// <returns></returns>
    public async Task ProcessCommissionPayments()
    {
        _log.Info($"ProcessCommissionPayments triggered at {DateTime.UtcNow}");
        var today = DateTime.UtcNow.Date;
        var day = today.Day;     // Lấy ngày
        var month = (short)today.Month; // Lấy tháng
        var year = (short)today.Year;   // Lấy năm

        // Lấy danh sách tất cả shop có Status = Active
        var shops = await _shopRepository.GetActivedShops();
        var shopCommissions = await _commissionsConfigRepository.FindCommissionsConfigs();

        // Kiểm tra từng shop
        foreach (var shop in shops)
        {
            var shopId = shop.ShopId;
            var commissionConfig = shopCommissions.FirstOrDefault(c => c.ShopId == shopId);

            if (commissionConfig == null)
            {
                _log.Info($"No commission config found for ShopId: {shopId}");
                continue;
            }

            int paymentDue = commissionConfig.AdvancedCommissionsConfig?.PaymentDue ?? 1; // Mặc định là 1 nếu không tìm thấy

            var currentMonthDate = today; // Lấy ngày tháng hiện tại
            if (day.Equals(paymentDue))
            {
                try
                {
                    // Gọi hàm ApprovePaymentForCommission cho shop này với trạng thái Paid, tháng hiện tại
                    await ApprovePaymentForCommission(shopId, currentMonthDate.Month, currentMonthDate.Year, PaymentStatus.Paid);
                    _log.Info($"ApprovePaymentForCommission executed for ShopId: {shopId} at {DateTime.UtcNow}");
                }
                catch (Exception ex)
                {
                    _log.Error($"Error executing ApprovePaymentForCommission for ShopId: {shopId}", ex);
                }
            }
            else
            {
                try
                {
                    // Nếu không phải ngày thanh toán, chỉ tính toán và để trạng thái Unpaid, tháng hiện tại
                    await ApprovePaymentForCommission(shopId, currentMonthDate.Month, currentMonthDate.Year, PaymentStatus.Unpaid);
                    _log.Info($"Calculated commission report for ShopId: {shopId} at {DateTime.UtcNow} (Unpaid)");
                }
                catch (Exception ex)
                {
                    _log.Error($"Error calculating commission report for ShopId: {shopId}", ex);
                }
            }
        }
    }

    /// <summary>
    /// Xử lý thanh toán hoa hồng cho một shop cụ thể.
    /// Chỉ lưu các bản ghi có CommissionValue > 0 để tối ưu storage và performance.
    /// </summary>
    /// <param name="shopId">ID của shop</param>
    /// <param name="month">Tháng</param>
    /// <param name="year">Năm</param>
    /// <param name="paymentStatus">Trạng thái thanh toán</param>
    /// <returns></returns>
    private async Task ApprovePaymentForCommission(string shopId, int month, int year, PaymentStatus paymentStatus)
    {
        var inputDto = new GetPartnerCommissionsByMonthYearInputDto
        {
            ShopId = shopId,
            Month = month,
            Year = year,
            Paging = new Paging
            {
                PageSize = int.MaxValue,
                PageIndex = 0,
                Search = ""
            }
        };

        // Kiểm tra xem đã có report trong database cho tháng/năm này chưa (bất kể trạng thái)
        var existingCommissionReport = await _commissionsReportRepository.GetCommissionsReport(inputDto);

        // Nếu đã có report với trạng thái Paid thì không làm gì
        if (existingCommissionReport != null && existingCommissionReport.Total > 0 &&
            existingCommissionReport.Result.Any(r => r.PaymentStatus == PaymentStatus.Paid))
        {
            _log.Info($"ShopId: {shopId} commission for month {month}/{year} has been paid - skipping");
            return;
        }

        // Nếu yêu cầu Paid mà hiện tại có Unpaid, thì cần xóa Unpaid và tạo mới Paid
        if (paymentStatus == PaymentStatus.Paid && existingCommissionReport != null && existingCommissionReport.Total > 0 &&
            existingCommissionReport.Result.Any(r => r.PaymentStatus == PaymentStatus.Unpaid))
        {
            _log.Info($"ShopId: {shopId} - Updating commission status from Unpaid to Paid for month {month}/{year}");
            await _commissionsReportRepository.DeleteCommissionReportsByShopMonthYear(shopId, month, year);
            existingCommissionReport = null; // Reset để không bị conflict ở logic sau
        }
        // lấy danh sách đối tác + hoa hồng theo tháng và năm
        var commissionReport = (await _commissionReportFlow.GetPartnerCommissionsByMonthAndYear(inputDto)).Result;

        if (commissionReport == null || commissionReport.Count == 0)
        {
            _log.Info($"No commission data found for ShopId: {shopId}, month: {month}/{year}");
            return;
        }

        var insertCommissionReportList = _mapper.Map<List<CommissionsReport>>(commissionReport)
                .Select(commission =>
                {
                    commission.ShopId = shopId;
                    commission.Month = month;
                    commission.Year = year;
                    commission.PaymentStatus = paymentStatus;
                    return commission;
                })
                .Where(commission => commission.CommissionValue > 0) // Chỉ lưu các bản ghi có CommissionValue > 0
                .ToList();

        if (insertCommissionReportList.Count == 0)
        {
            _log.Info($"No commission records with CommissionValue > 0 found for ShopId: {shopId}, month: {month}/{year} - skipping insert");
            return;
        }

        // Lọc bỏ các bản ghi đã tồn tại để tránh duplicate
        var existingUserIds = existingCommissionReport?.Result?.Select(r => r.UserId).ToHashSet() ?? [];
        var newCommissionReports = insertCommissionReportList
            .Where(commission => !existingUserIds.Contains(commission.UserId))
            .ToList();

        if (newCommissionReports.Count == 0)
        {
            _log.Info($"All commission records already exist for ShopId: {shopId}, month: {month}/{year} - skipping insert");
            return;
        }

        // Insert commission reports
        await _commissionsReportRepository.InsertCommissionReports(newCommissionReports);
        _log.Info($"Successfully inserted {newCommissionReports.Count} new commission reports (CommissionValue > 0) for ShopId: {shopId}, month: {month}/{year}, status: {paymentStatus}");
    }

    public async Task DeactivateExpiredAffiliationUsers()
    {
        try
        {
            // Lấy danh sách tất cả shop có Status = Active
            var shops = await _shopRepository.GetActivedShops();
            var shopCommissions = await _commissionsConfigRepository.FindCommissionsConfigs();

            // Kiểm tra từng shop
            foreach (var shop in shops)
            {
                var shopId = shop.ShopId;
                var commissionConfig = shopCommissions.FirstOrDefault(c => c.ShopId == shopId);

                if (commissionConfig == null)
                    continue;

                var expiredDateSinceBecomeAffiliate = commissionConfig.AdvancedCommissionsConfig?.PartnerCommExpiry;

                if (expiredDateSinceBecomeAffiliate == null)
                    continue;

                var expiredUsers = _userRepository.GetListUserExpireAffiliate(shopId, expiredDateSinceBecomeAffiliate.Value);

                foreach (var user in expiredUsers)
                {
                    user.AffiliationStatus = AffiliationTypeStatus.Expired;
                    _userRepository.UpdateUser(user);
                }
            }
        }
        catch (Exception ex)
        {
            _log.Error($"DeactivateExpiredAffiliationUsers Error: {ex.Message}", ex);
            throw;
        }
    }
}